/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部导航 */
header {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

header h1 {
    text-align: center;
    color: #2c3e50;
    margin-bottom: 20px;
}

nav {
    display: flex;
    justify-content: center;
    gap: 10px;
}

.nav-btn {
    padding: 12px 24px;
    border: 2px solid #3498db;
    background: white;
    color: #3498db;
    border-radius: 25px;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.3s ease;
}

.nav-btn.active,
.nav-btn:hover {
    background: #3498db;
    color: white;
}

/* 视图切换 */
.view {
    display: none;
}

.view.active {
    display: block;
}

/* 手机端表单样式 */
.form-container {
    background: white;
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    max-width: 500px;
    margin: 0 auto;
}

.form-container h2 {
    text-align: center;
    color: #2c3e50;
    margin-bottom: 30px;
}

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #555;
}

.form-group input[type="text"] {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.form-group input[type="text"]:focus {
    outline: none;
    border-color: #3498db;
}

/* 单选按钮样式 */
.radio-group {
    display: flex;
    gap: 20px;
}

.radio-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-weight: normal;
}

.radio-label input[type="radio"] {
    margin-right: 8px;
    transform: scale(1.2);
}

/* 照片相关样式 */
.photo-section {
    text-align: center;
}

.photo-btn {
    background: #27ae60;
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 8px;
    font-size: 16px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.photo-btn:hover {
    background: #219a52;
}

.photo-preview {
    margin-top: 15px;
    text-align: center;
}

.photo-preview img {
    max-width: 100%;
    max-height: 300px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.retake-btn {
    margin-top: 10px;
    background: #e74c3c;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 5px;
    cursor: pointer;
}

/* 表单操作按钮 */
.form-actions {
    display: flex;
    gap: 15px;
    margin-top: 30px;
}

.save-btn, .clear-btn {
    flex: 1;
    padding: 15px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.save-btn {
    background: #3498db;
    color: white;
}

.save-btn:hover {
    background: #2980b9;
}

.clear-btn {
    background: #95a5a6;
    color: white;
}

.clear-btn:hover {
    background: #7f8c8d;
}

/* 状态消息 */
.status-message {
    margin-top: 20px;
    padding: 15px;
    border-radius: 8px;
    text-align: center;
    font-weight: 600;
    display: none;
}

.status-message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* 平板端数据查看样式 */
.data-container {
    background: white;
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.data-container h2 {
    color: #2c3e50;
    margin-bottom: 30px;
    text-align: center;
}

.controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.search-section, .filter-section {
    display: flex;
    gap: 10px;
    align-items: center;
}

.search-section input,
.filter-section select {
    padding: 10px;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
}

.search-section button,
.filter-section button {
    padding: 10px 20px;
    background: #3498db;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

.data-stats {
    margin-bottom: 20px;
    color: #666;
    font-weight: 600;
}

/* 数据列表样式 */
.data-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.data-item {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    border: 1px solid #e9ecef;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    cursor: pointer;
}

.data-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.data-item img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 15px;
}

.data-item h4 {
    color: #2c3e50;
    margin-bottom: 5px;
}

.data-item p {
    color: #666;
    margin-bottom: 3px;
}

.data-item .date {
    font-size: 12px;
    color: #999;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 30px;
    border-radius: 10px;
    width: 90%;
    max-width: 600px;
    position: relative;
}

.close {
    position: absolute;
    right: 20px;
    top: 15px;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    color: #999;
}

.close:hover {
    color: #333;
}

.modal-actions {
    margin-top: 20px;
    display: flex;
    gap: 10px;
    justify-content: center;
}

.edit-btn, .delete-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
}

.edit-btn {
    background: #f39c12;
    color: white;
}

.delete-btn {
    background: #e74c3c;
    color: white;
}

/* 分页样式 */
.pagination {
    display: flex;
    justify-content: center;
    gap: 10px;
}

.pagination button {
    padding: 8px 12px;
    border: 1px solid #ddd;
    background: white;
    cursor: pointer;
    border-radius: 5px;
}

.pagination button.active {
    background: #3498db;
    color: white;
    border-color: #3498db;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .form-container {
        padding: 20px;
    }
    
    .controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-section, .filter-section {
        justify-content: center;
    }
    
    .data-list {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .modal-content {
        width: 95%;
        margin: 10% auto;
        padding: 20px;
    }
}

@media (max-width: 480px) {
    .nav-btn {
        padding: 10px 16px;
        font-size: 14px;
    }
    
    .radio-group {
        flex-direction: column;
        gap: 10px;
    }
}
