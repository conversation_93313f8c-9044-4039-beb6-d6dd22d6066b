// 数据库管理模块
class DatabaseManager {
    constructor() {
        this.dbName = 'DataCollectionDB';
        this.version = 1;
        this.db = null;
        this.storeName = 'userInfo';
    }

    // 初始化数据库
    async init() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.version);

            request.onerror = () => {
                console.error('数据库打开失败:', request.error);
                reject(request.error);
            };

            request.onsuccess = () => {
                this.db = request.result;
                console.log('数据库连接成功');
                resolve(this.db);
            };

            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                
                // 创建用户信息表
                if (!db.objectStoreNames.contains(this.storeName)) {
                    const store = db.createObjectStore(this.storeName, {
                        keyPath: 'id',
                        autoIncrement: true
                    });

                    // 创建索引
                    store.createIndex('name', 'name', { unique: false });
                    store.createIndex('gender', 'gender', { unique: false });
                    store.createIndex('createTime', 'createTime', { unique: false });
                    
                    console.log('数据库表创建成功');
                }
            };
        });
    }

    // 保存用户数据
    async saveUser(userData) {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                reject(new Error('数据库未初始化'));
                return;
            }

            const transaction = this.db.transaction([this.storeName], 'readwrite');
            const store = transaction.objectStore(this.storeName);

            // 添加时间戳
            userData.createTime = new Date().toISOString();
            userData.updateTime = userData.createTime;

            const request = store.add(userData);

            request.onsuccess = () => {
                console.log('用户数据保存成功, ID:', request.result);
                resolve(request.result);
            };

            request.onerror = () => {
                console.error('用户数据保存失败:', request.error);
                reject(request.error);
            };
        });
    }

    // 获取所有用户数据
    async getAllUsers() {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                reject(new Error('数据库未初始化'));
                return;
            }

            const transaction = this.db.transaction([this.storeName], 'readonly');
            const store = transaction.objectStore(this.storeName);
            const request = store.getAll();

            request.onsuccess = () => {
                resolve(request.result);
            };

            request.onerror = () => {
                console.error('获取用户数据失败:', request.error);
                reject(request.error);
            };
        });
    }

    // 根据ID获取用户数据
    async getUserById(id) {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                reject(new Error('数据库未初始化'));
                return;
            }

            const transaction = this.db.transaction([this.storeName], 'readonly');
            const store = transaction.objectStore(this.storeName);
            const request = store.get(id);

            request.onsuccess = () => {
                resolve(request.result);
            };

            request.onerror = () => {
                console.error('获取用户数据失败:', request.error);
                reject(request.error);
            };
        });
    }

    // 更新用户数据
    async updateUser(id, userData) {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                reject(new Error('数据库未初始化'));
                return;
            }

            const transaction = this.db.transaction([this.storeName], 'readwrite');
            const store = transaction.objectStore(this.storeName);

            // 先获取现有数据
            const getRequest = store.get(id);
            
            getRequest.onsuccess = () => {
                const existingData = getRequest.result;
                if (!existingData) {
                    reject(new Error('用户不存在'));
                    return;
                }

                // 合并数据并更新时间戳
                const updatedData = {
                    ...existingData,
                    ...userData,
                    id: id,
                    updateTime: new Date().toISOString()
                };

                const putRequest = store.put(updatedData);
                
                putRequest.onsuccess = () => {
                    console.log('用户数据更新成功');
                    resolve(putRequest.result);
                };

                putRequest.onerror = () => {
                    console.error('用户数据更新失败:', putRequest.error);
                    reject(putRequest.error);
                };
            };

            getRequest.onerror = () => {
                console.error('获取用户数据失败:', getRequest.error);
                reject(getRequest.error);
            };
        });
    }

    // 删除用户数据
    async deleteUser(id) {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                reject(new Error('数据库未初始化'));
                return;
            }

            const transaction = this.db.transaction([this.storeName], 'readwrite');
            const store = transaction.objectStore(this.storeName);
            const request = store.delete(id);

            request.onsuccess = () => {
                console.log('用户数据删除成功');
                resolve();
            };

            request.onerror = () => {
                console.error('用户数据删除失败:', request.error);
                reject(request.error);
            };
        });
    }

    // 搜索用户数据
    async searchUsers(searchTerm, genderFilter = '') {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                reject(new Error('数据库未初始化'));
                return;
            }

            const transaction = this.db.transaction([this.storeName], 'readonly');
            const store = transaction.objectStore(this.storeName);
            const request = store.getAll();

            request.onsuccess = () => {
                let results = request.result;

                // 按姓名搜索
                if (searchTerm) {
                    results = results.filter(user => 
                        user.name.toLowerCase().includes(searchTerm.toLowerCase())
                    );
                }

                // 按性别筛选
                if (genderFilter) {
                    results = results.filter(user => user.gender === genderFilter);
                }

                // 按创建时间倒序排列
                results.sort((a, b) => new Date(b.createTime) - new Date(a.createTime));

                resolve(results);
            };

            request.onerror = () => {
                console.error('搜索用户数据失败:', request.error);
                reject(request.error);
            };
        });
    }

    // 获取统计信息
    async getStats() {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                reject(new Error('数据库未初始化'));
                return;
            }

            const transaction = this.db.transaction([this.storeName], 'readonly');
            const store = transaction.objectStore(this.storeName);
            const request = store.getAll();

            request.onsuccess = () => {
                const users = request.result;
                const stats = {
                    total: users.length,
                    male: users.filter(u => u.gender === '男').length,
                    female: users.filter(u => u.gender === '女').length,
                    today: users.filter(u => {
                        const today = new Date().toDateString();
                        const userDate = new Date(u.createTime).toDateString();
                        return today === userDate;
                    }).length
                };

                resolve(stats);
            };

            request.onerror = () => {
                console.error('获取统计信息失败:', request.error);
                reject(request.error);
            };
        });
    }

    // 清空所有数据
    async clearAllData() {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                reject(new Error('数据库未初始化'));
                return;
            }

            const transaction = this.db.transaction([this.storeName], 'readwrite');
            const store = transaction.objectStore(this.storeName);
            const request = store.clear();

            request.onsuccess = () => {
                console.log('所有数据已清空');
                resolve();
            };

            request.onerror = () => {
                console.error('清空数据失败:', request.error);
                reject(request.error);
            };
        });
    }
}

// 创建全局数据库实例
const dbManager = new DatabaseManager();
