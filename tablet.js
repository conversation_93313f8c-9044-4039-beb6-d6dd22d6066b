// 平板端数据查看功能
class TabletController {
    constructor() {
        this.currentData = [];
        this.filteredData = [];
        this.currentPage = 1;
        this.itemsPerPage = 12;
        this.currentUser = null;
        this.initElements();
        this.bindEvents();
    }

    initElements() {
        this.searchInput = document.getElementById('searchInput');
        this.searchBtn = document.getElementById('searchBtn');
        this.genderFilter = document.getElementById('genderFilter');
        this.exportBtn = document.getElementById('exportBtn');
        this.totalCount = document.getElementById('totalCount');
        this.dataList = document.getElementById('dataList');
        this.pagination = document.getElementById('pagination');
        this.detailModal = document.getElementById('detailModal');
        this.detailContent = document.getElementById('detailContent');
        this.editBtn = document.getElementById('editBtn');
        this.deleteBtn = document.getElementById('deleteBtn');
        this.closeModal = document.querySelector('.close');
    }

    bindEvents() {
        // 搜索功能
        this.searchBtn.addEventListener('click', () => this.performSearch());
        this.searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.performSearch();
        });

        // 筛选功能
        this.genderFilter.addEventListener('change', () => this.performSearch());

        // 导出功能
        this.exportBtn.addEventListener('click', () => this.exportData());

        // 模态框事件
        this.closeModal.addEventListener('click', () => this.closeDetailModal());
        this.detailModal.addEventListener('click', (e) => {
            if (e.target === this.detailModal) this.closeDetailModal();
        });

        // 详情操作按钮
        this.editBtn.addEventListener('click', () => this.editUser());
        this.deleteBtn.addEventListener('click', () => this.deleteUser());

        // 键盘事件
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') this.closeDetailModal();
        });
    }

    // 加载数据
    async loadData() {
        try {
            this.currentData = await dbManager.getAllUsers();
            this.performSearch();
            this.updateStats();
        } catch (error) {
            console.error('加载数据失败:', error);
            this.showMessage('加载数据失败', 'error');
        }
    }

    // 执行搜索和筛选
    async performSearch() {
        try {
            const searchTerm = this.searchInput.value.trim();
            const genderFilter = this.genderFilter.value;

            this.filteredData = await dbManager.searchUsers(searchTerm, genderFilter);
            this.currentPage = 1;
            this.renderDataList();
            this.renderPagination();
            this.updateStats();

        } catch (error) {
            console.error('搜索失败:', error);
            this.showMessage('搜索失败', 'error');
        }
    }

    // 渲染数据列表
    renderDataList() {
        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const pageData = this.filteredData.slice(startIndex, endIndex);

        if (pageData.length === 0) {
            this.dataList.innerHTML = '<div class="no-data">暂无数据</div>';
            return;
        }

        this.dataList.innerHTML = pageData.map(user => this.createDataItem(user)).join('');

        // 绑定点击事件
        this.dataList.querySelectorAll('.data-item').forEach((item, index) => {
            item.addEventListener('click', () => {
                const userIndex = startIndex + index;
                this.showUserDetail(this.filteredData[userIndex]);
            });
        });
    }

    // 创建数据项HTML
    createDataItem(user) {
        const createTime = new Date(user.createTime).toLocaleString('zh-CN');
        const photoSrc = user.photo ? user.photo.data : 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik00MCA0NkM0Ni42Mjc0IDQ2IDUyIDQwLjYyNzQgNTIgMzRDNTIgMjcuMzcyNiA0Ni42Mjc0IDIyIDQwIDIyQzMzLjM3MjYgMjIgMjggMjcuMzcyNiAyOCAzNEMyOCA0MC42Mjc0IDMzLjM3MjYgNDYgNDAgNDZaIiBmaWxsPSIjQ0NDQ0NDIi8+CjxwYXRoIGQ9Ik00MCA1MEMzMC4wNTg5IDUwIDIyIDU4LjA1ODkgMjIgNjhINThDNTggNTguMDU4OSA0OS45NDExIDUwIDQwIDUwWiIgZmlsbD0iI0NDQ0NDQyIvPgo8L3N2Zz4K';

        return `
            <div class="data-item" data-id="${user.id}">
                <img src="${photoSrc}" alt="${user.name}" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik00MCA0NkM0Ni42Mjc0IDQ2IDUyIDQwLjYyNzQgNTIgMzRDNTIgMjcuMzcyNiA0Ni42Mjc0IDIyIDQwIDIyQzMzLjM3MjYgMjIgMjggMjcuMzcyNiAyOCAzNEMyOCA0MC42Mjc0IDMzLjM3MjYgNDYgNDAgNDZaIiBmaWxsPSIjQ0NDQ0NDIi8+CjxwYXRoIGQ9Ik00MCA1MEMzMC4wNTg5IDUwIDIyIDU4LjA1ODkgMjIgNjhINThDNTggNTguMDU4OSA0OS45NDExIDUwIDQwIDUwWiIgZmlsbD0iI0NDQ0NDQyIvPgo8L3N2Zz4K'">
                <h4>${this.escapeHtml(user.name)}</h4>
                <p>性别: ${user.gender}</p>
                <p class="date">创建时间: ${createTime}</p>
            </div>
        `;
    }

    // 渲染分页
    renderPagination() {
        const totalPages = Math.ceil(this.filteredData.length / this.itemsPerPage);
        
        if (totalPages <= 1) {
            this.pagination.innerHTML = '';
            return;
        }

        let paginationHTML = '';

        // 上一页按钮
        if (this.currentPage > 1) {
            paginationHTML += `<button onclick="tabletController.goToPage(${this.currentPage - 1})">上一页</button>`;
        }

        // 页码按钮
        const startPage = Math.max(1, this.currentPage - 2);
        const endPage = Math.min(totalPages, this.currentPage + 2);

        if (startPage > 1) {
            paginationHTML += `<button onclick="tabletController.goToPage(1)">1</button>`;
            if (startPage > 2) {
                paginationHTML += `<span>...</span>`;
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            const activeClass = i === this.currentPage ? 'active' : '';
            paginationHTML += `<button class="${activeClass}" onclick="tabletController.goToPage(${i})">${i}</button>`;
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                paginationHTML += `<span>...</span>`;
            }
            paginationHTML += `<button onclick="tabletController.goToPage(${totalPages})">${totalPages}</button>`;
        }

        // 下一页按钮
        if (this.currentPage < totalPages) {
            paginationHTML += `<button onclick="tabletController.goToPage(${this.currentPage + 1})">下一页</button>`;
        }

        this.pagination.innerHTML = paginationHTML;
    }

    // 跳转到指定页面
    goToPage(page) {
        this.currentPage = page;
        this.renderDataList();
        this.renderPagination();
    }

    // 更新统计信息
    async updateStats() {
        try {
            const stats = await dbManager.getStats();
            this.totalCount.textContent = `总计: ${this.filteredData.length} 条记录 (男: ${stats.male}, 女: ${stats.female}, 今日新增: ${stats.today})`;
        } catch (error) {
            console.error('更新统计失败:', error);
            this.totalCount.textContent = `总计: ${this.filteredData.length} 条记录`;
        }
    }

    // 显示用户详情
    showUserDetail(user) {
        this.currentUser = user;
        const createTime = new Date(user.createTime).toLocaleString('zh-CN');
        const updateTime = new Date(user.updateTime).toLocaleString('zh-CN');
        const photoSrc = user.photo ? user.photo.data : '';

        this.detailContent.innerHTML = `
            <div class="detail-info">
                <div class="detail-photo">
                    ${photoSrc ? `<img src="${photoSrc}" alt="${user.name}" style="max-width: 200px; max-height: 200px; border-radius: 10px;">` : '<div class="no-photo">无照片</div>'}
                </div>
                <div class="detail-text">
                    <p><strong>ID:</strong> ${user.id}</p>
                    <p><strong>姓名:</strong> ${this.escapeHtml(user.name)}</p>
                    <p><strong>性别:</strong> ${user.gender}</p>
                    <p><strong>创建时间:</strong> ${createTime}</p>
                    <p><strong>更新时间:</strong> ${updateTime}</p>
                    ${user.photo ? `<p><strong>照片大小:</strong> ${this.formatFileSize(user.photo.size)}</p>` : ''}
                </div>
            </div>
        `;

        this.detailModal.style.display = 'block';
    }

    // 关闭详情模态框
    closeDetailModal() {
        this.detailModal.style.display = 'none';
        this.currentUser = null;
    }

    // 编辑用户
    editUser() {
        if (!this.currentUser) return;

        const newName = prompt('请输入新姓名:', this.currentUser.name);
        if (newName === null || newName.trim() === '') return;

        const newGender = prompt('请输入性别 (男/女):', this.currentUser.gender);
        if (newGender === null || !['男', '女'].includes(newGender)) return;

        this.updateUser(this.currentUser.id, {
            name: newName.trim(),
            gender: newGender
        });
    }

    // 更新用户数据
    async updateUser(id, userData) {
        try {
            await dbManager.updateUser(id, userData);
            this.showMessage('用户信息更新成功', 'success');
            this.closeDetailModal();
            this.loadData();
        } catch (error) {
            console.error('更新用户失败:', error);
            this.showMessage('更新用户失败', 'error');
        }
    }

    // 删除用户
    async deleteUser() {
        if (!this.currentUser) return;

        if (!confirm(`确定要删除用户 "${this.currentUser.name}" 吗？此操作不可恢复。`)) {
            return;
        }

        try {
            await dbManager.deleteUser(this.currentUser.id);
            this.showMessage('用户删除成功', 'success');
            this.closeDetailModal();
            this.loadData();
        } catch (error) {
            console.error('删除用户失败:', error);
            this.showMessage('删除用户失败', 'error');
        }
    }

    // 导出数据
    async exportData() {
        try {
            const data = this.filteredData.map(user => ({
                ID: user.id,
                姓名: user.name,
                性别: user.gender,
                创建时间: new Date(user.createTime).toLocaleString('zh-CN'),
                更新时间: new Date(user.updateTime).toLocaleString('zh-CN')
            }));

            const csv = this.convertToCSV(data);
            this.downloadCSV(csv, `用户数据_${new Date().toISOString().split('T')[0]}.csv`);
            
            this.showMessage('数据导出成功', 'success');
        } catch (error) {
            console.error('导出数据失败:', error);
            this.showMessage('导出数据失败', 'error');
        }
    }

    // 转换为CSV格式
    convertToCSV(data) {
        if (data.length === 0) return '';

        const headers = Object.keys(data[0]);
        const csvContent = [
            headers.join(','),
            ...data.map(row => headers.map(header => `"${row[header]}"`).join(','))
        ].join('\n');

        return '\uFEFF' + csvContent; // 添加BOM以支持中文
    }

    // 下载CSV文件
    downloadCSV(csv, filename) {
        const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        
        if (link.download !== undefined) {
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', filename);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    }

    // HTML转义
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // 格式化文件大小
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // 显示消息
    showMessage(message, type = 'success') {
        // 创建临时消息元素
        const messageEl = document.createElement('div');
        messageEl.className = `status-message ${type}`;
        messageEl.textContent = message;
        messageEl.style.position = 'fixed';
        messageEl.style.top = '20px';
        messageEl.style.right = '20px';
        messageEl.style.zIndex = '9999';
        messageEl.style.display = 'block';

        document.body.appendChild(messageEl);

        setTimeout(() => {
            document.body.removeChild(messageEl);
        }, 3000);
    }
}
