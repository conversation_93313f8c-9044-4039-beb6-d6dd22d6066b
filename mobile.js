// 手机端数据录入功能
class MobileController {
    constructor() {
        this.currentPhoto = null;
        this.initElements();
        this.bindEvents();
    }

    initElements() {
        this.form = document.getElementById('userForm');
        this.nameInput = document.getElementById('userName');
        this.genderInputs = document.querySelectorAll('input[name="gender"]');
        this.photoInput = document.getElementById('photoInput');
        this.takePhotoBtn = document.getElementById('takePhotoBtn');
        this.photoPreview = document.getElementById('photoPreview');
        this.previewImg = document.getElementById('previewImg');
        this.retakeBtn = document.getElementById('retakeBtn');
        this.saveBtn = document.getElementById('saveBtn');
        this.clearBtn = document.getElementById('clearBtn');
        this.statusMessage = document.getElementById('statusMessage');
    }

    bindEvents() {
        // 拍照按钮事件
        this.takePhotoBtn.addEventListener('click', () => this.takePhoto());
        
        // 重新拍照按钮事件
        this.retakeBtn.addEventListener('click', () => this.retakePhoto());
        
        // 文件输入变化事件
        this.photoInput.addEventListener('change', (e) => this.handlePhotoSelect(e));
        
        // 表单提交事件
        this.form.addEventListener('submit', (e) => this.handleSubmit(e));
        
        // 清空表单事件
        this.clearBtn.addEventListener('click', () => this.clearForm());
        
        // 输入验证事件
        this.nameInput.addEventListener('input', () => this.validateForm());
        this.genderInputs.forEach(input => {
            input.addEventListener('change', () => this.validateForm());
        });
    }

    // 拍照功能
    takePhoto() {
        // 检查是否支持相机
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
            this.showMessage('您的设备不支持相机功能，请选择文件上传', 'error');
            this.photoInput.click();
            return;
        }

        // 触发文件选择（在移动设备上会调用相机）
        this.photoInput.click();
    }

    // 重新拍照
    retakePhoto() {
        this.currentPhoto = null;
        this.photoPreview.style.display = 'none';
        this.takePhotoBtn.style.display = 'block';
        this.validateForm();
    }

    // 处理照片选择
    handlePhotoSelect(event) {
        const file = event.target.files[0];
        if (!file) return;

        // 验证文件类型
        if (!file.type.startsWith('image/')) {
            this.showMessage('请选择图片文件', 'error');
            return;
        }

        // 验证文件大小（限制为5MB）
        const maxSize = 5 * 1024 * 1024;
        if (file.size > maxSize) {
            this.showMessage('图片文件过大，请选择小于5MB的图片', 'error');
            return;
        }

        this.processPhoto(file);
    }

    // 处理照片
    async processPhoto(file) {
        try {
            // 压缩图片
            const compressedFile = await this.compressImage(file);
            
            // 转换为Base64
            const base64 = await this.fileToBase64(compressedFile);
            
            this.currentPhoto = {
                data: base64,
                name: file.name,
                size: compressedFile.size,
                type: compressedFile.type
            };

            // 显示预览
            this.showPhotoPreview(base64);
            this.validateForm();

        } catch (error) {
            console.error('照片处理失败:', error);
            this.showMessage('照片处理失败，请重试', 'error');
        }
    }

    // 压缩图片
    compressImage(file, maxWidth = 800, maxHeight = 600, quality = 0.8) {
        return new Promise((resolve) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();

            img.onload = () => {
                // 计算压缩后的尺寸
                let { width, height } = img;
                
                if (width > maxWidth || height > maxHeight) {
                    const ratio = Math.min(maxWidth / width, maxHeight / height);
                    width *= ratio;
                    height *= ratio;
                }

                canvas.width = width;
                canvas.height = height;

                // 绘制压缩后的图片
                ctx.drawImage(img, 0, 0, width, height);

                // 转换为Blob
                canvas.toBlob(resolve, file.type, quality);
            };

            img.src = URL.createObjectURL(file);
        });
    }

    // 文件转Base64
    fileToBase64(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => resolve(reader.result);
            reader.onerror = reject;
            reader.readAsDataURL(file);
        });
    }

    // 显示照片预览
    showPhotoPreview(base64) {
        this.previewImg.src = base64;
        this.photoPreview.style.display = 'block';
        this.takePhotoBtn.style.display = 'none';
    }

    // 表单验证
    validateForm() {
        const name = this.nameInput.value.trim();
        const gender = document.querySelector('input[name="gender"]:checked');
        const hasPhoto = this.currentPhoto !== null;

        const isValid = name && gender && hasPhoto;
        this.saveBtn.disabled = !isValid;
        
        return isValid;
    }

    // 处理表单提交
    async handleSubmit(event) {
        event.preventDefault();

        if (!this.validateForm()) {
            this.showMessage('请填写完整信息并拍照', 'error');
            return;
        }

        try {
            this.saveBtn.disabled = true;
            this.saveBtn.textContent = '保存中...';

            // 收集表单数据
            const userData = {
                name: this.nameInput.value.trim(),
                gender: document.querySelector('input[name="gender"]:checked').value,
                photo: this.currentPhoto
            };

            // 保存到数据库
            const id = await dbManager.saveUser(userData);
            
            this.showMessage(`数据保存成功！记录ID: ${id}`, 'success');
            this.clearForm();

        } catch (error) {
            console.error('保存失败:', error);
            this.showMessage('保存失败，请重试', 'error');
        } finally {
            this.saveBtn.disabled = false;
            this.saveBtn.textContent = '保存数据';
        }
    }

    // 清空表单
    clearForm() {
        this.form.reset();
        this.currentPhoto = null;
        this.photoPreview.style.display = 'none';
        this.takePhotoBtn.style.display = 'block';
        this.validateForm();
        this.hideMessage();
    }

    // 显示状态消息
    showMessage(message, type = 'success') {
        this.statusMessage.textContent = message;
        this.statusMessage.className = `status-message ${type}`;
        this.statusMessage.style.display = 'block';

        // 3秒后自动隐藏成功消息
        if (type === 'success') {
            setTimeout(() => this.hideMessage(), 3000);
        }
    }

    // 隐藏状态消息
    hideMessage() {
        this.statusMessage.style.display = 'none';
    }

    // 检查网络状态
    checkNetworkStatus() {
        if (!navigator.onLine) {
            this.showMessage('当前离线状态，数据将保存在本地', 'warning');
        }
    }

    // 获取设备信息
    getDeviceInfo() {
        return {
            userAgent: navigator.userAgent,
            platform: navigator.platform,
            language: navigator.language,
            online: navigator.onLine,
            timestamp: new Date().toISOString()
        };
    }
}

// 工具函数
const MobileUtils = {
    // 检测是否为移动设备
    isMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    },

    // 检测是否支持相机
    supportCamera() {
        return !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);
    },

    // 格式化文件大小
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    // 生成唯一ID
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }
};
