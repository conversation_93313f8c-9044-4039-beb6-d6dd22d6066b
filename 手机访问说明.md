# 📱 手机访问数据采集系统说明

## 🌐 访问地址

**在手机浏览器中输入以下地址：**

```
http://************:8000
```

## 📋 详细步骤

### 1. 确保设备连接
- ✅ 电脑和手机必须连接到同一个WiFi网络
- ✅ 当前电脑IP地址：`************`
- ✅ 服务器已启动在端口8000

### 2. 在手机上访问
1. 打开手机浏览器（Chrome、Safari等）
2. 在地址栏输入：`http://************:8000`
3. 按回车键访问

### 3. 如果无法访问，请检查：

#### 🔥 防火墙设置
Windows可能阻止了端口访问，需要：
1. 按 `Win + R` 打开运行对话框
2. 输入 `wf.msc` 打开Windows防火墙
3. 点击"入站规则" → "新建规则"
4. 选择"端口" → "TCP" → 输入端口号 `8000`
5. 选择"允许连接" → 完成设置

#### 📶 网络连接
- 确保电脑和手机在同一WiFi网络
- 尝试ping测试：在手机浏览器输入 `http://************:8000`

#### 🔄 重启服务
如果还是不行，可以重启服务器：
```bash
# 停止当前服务
Ctrl + C

# 重新启动
python -m http.server 8000 --bind 0.0.0.0
```

## 🎯 快速测试

### 方法1：二维码访问
你可以用手机扫描二维码快速访问：
```
█████████████████████████████████
█████████████████████████████████
████ ▄▄▄▄▄ █▀█ █▄▀▄▀█ ▄▄▄▄▄ ████
████ █   █ █▀▀▀█ ▀▄▀ █ █   █ ████
████ █▄▄▄█ █▀ █▀▀▄▄▀▄█ █▄▄▄█ ████
████▄▄▄▄▄▄▄█▄▀ ▀▄█ █▄█▄▄▄▄▄▄▄████
████▄▄  ▄▄▄  ▄▀▀▄▀▄▀▄▄█ ▀▄█▄▄████
█████▀▄▄▀▄▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄████
████▄▄██▄▄▄█▄▄▄█▄▄▄█▄▄▄█▄▄▄█▄████
█████████████████████████████████
█████████████████████████████████
```

### 方法2：局域网扫描
在手机上下载"Fing"等网络扫描应用，查找IP为 `************` 的设备。

## 🚀 成功访问后

1. **手机端录入**：点击"手机端录入"开始使用
2. **查看指南**：点击"📱 手机端使用指南"了解详细功能
3. **开始体验**：
   - 输入姓名（支持语音输入）
   - 选择性别
   - 拍照（直接调用相机）
   - 保存数据

## 🔧 故障排除

### 常见问题：

1. **"无法访问此网站"**
   - 检查IP地址是否正确
   - 确认电脑和手机在同一WiFi

2. **"连接超时"**
   - 检查Windows防火墙设置
   - 确认服务器正在运行

3. **页面加载缓慢**
   - 这是正常的，因为是本地网络传输
   - 等待几秒钟即可

### 高级选项：

如果以上方法都不行，可以尝试：

1. **使用其他端口**：
   ```bash
   python -m http.server 3000 --bind 0.0.0.0
   ```
   然后访问：`http://************:3000`

2. **使用ngrok（需要安装）**：
   ```bash
   ngrok http 8000
   ```
   会生成一个公网地址，手机可以直接访问

## 📞 需要帮助？

如果仍然无法访问，请告诉我：
1. 手机浏览器显示的具体错误信息
2. 电脑和手机是否连接同一WiFi
3. 是否已经设置防火墙规则

---

**当前服务状态：** ✅ 运行中  
**服务地址：** http://************:8000  
**更新时间：** 2024-08-25
