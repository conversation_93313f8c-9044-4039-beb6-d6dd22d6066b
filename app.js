// 主应用控制器
class AppController {
    constructor() {
        this.currentView = 'mobile';
        this.mobileController = null;
        this.tabletController = null;
        this.init();
    }

    async init() {
        try {
            // 初始化数据库
            await dbManager.init();
            console.log('数据库初始化成功');

            // 初始化界面元素
            this.initElements();
            
            // 绑定事件
            this.bindEvents();
            
            // 初始化控制器
            this.initControllers();
            
            // 设置初始视图
            this.showView('mobile');
            
            // 检查设备类型并给出建议
            this.checkDeviceAndSuggest();

        } catch (error) {
            console.error('应用初始化失败:', error);
            this.showError('应用初始化失败，请刷新页面重试');
        }
    }

    initElements() {
        this.mobileBtn = document.getElementById('mobileBtn');
        this.tabletBtn = document.getElementById('tabletBtn');
        this.mobileView = document.getElementById('mobileView');
        this.tabletView = document.getElementById('tabletView');
    }

    bindEvents() {
        // 视图切换按钮事件
        this.mobileBtn.addEventListener('click', () => this.showView('mobile'));
        this.tabletBtn.addEventListener('click', () => this.showView('tablet'));

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case '1':
                        e.preventDefault();
                        this.showView('mobile');
                        break;
                    case '2':
                        e.preventDefault();
                        this.showView('tablet');
                        break;
                }
            }
        });

        // 网络状态监听
        window.addEventListener('online', () => {
            this.showNetworkStatus('网络已连接', 'success');
        });

        window.addEventListener('offline', () => {
            this.showNetworkStatus('网络已断开，数据将保存在本地', 'warning');
        });

        // 页面可见性变化监听
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden && this.currentView === 'tablet') {
                // 页面重新可见时刷新平板端数据
                this.tabletController?.loadData();
            }
        });
    }

    initControllers() {
        // 初始化手机端控制器
        this.mobileController = new MobileController();
        
        // 初始化平板端控制器
        this.tabletController = new TabletController();
    }

    // 显示指定视图
    showView(viewName) {
        // 更新当前视图
        this.currentView = viewName;

        // 更新导航按钮状态
        this.mobileBtn.classList.toggle('active', viewName === 'mobile');
        this.tabletBtn.classList.toggle('active', viewName === 'tablet');

        // 显示/隐藏视图
        this.mobileView.classList.toggle('active', viewName === 'mobile');
        this.tabletView.classList.toggle('active', viewName === 'tablet');

        // 执行视图特定的初始化
        if (viewName === 'tablet') {
            // 切换到平板端时加载数据
            setTimeout(() => {
                this.tabletController.loadData();
            }, 100);
        } else if (viewName === 'mobile') {
            // 切换到手机端时重置表单验证
            setTimeout(() => {
                this.mobileController.validateForm();
            }, 100);
        }

        // 更新页面标题
        document.title = viewName === 'mobile' ? '数据采集系统 - 录入' : '数据采集系统 - 查看';
    }

    // 检查设备类型并给出建议
    checkDeviceAndSuggest() {
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        const isTablet = /iPad|Android(?=.*\bMobile\b)/i.test(navigator.userAgent) || 
                         (navigator.userAgent.includes('Android') && !navigator.userAgent.includes('Mobile'));

        if (isMobile && !isTablet) {
            // 手机设备，建议使用录入功能
            this.showDeviceSuggestion('检测到手机设备，建议使用数据录入功能', 'info');
        } else if (isTablet || window.innerWidth >= 768) {
            // 平板或大屏设备，建议使用查看功能
            this.showDeviceSuggestion('检测到平板/大屏设备，建议使用数据查看功能', 'info');
            this.showView('tablet');
        }
    }

    // 显示设备建议
    showDeviceSuggestion(message, type = 'info') {
        const suggestionEl = document.createElement('div');
        suggestionEl.className = `device-suggestion ${type}`;
        suggestionEl.innerHTML = `
            <span>${message}</span>
            <button onclick="this.parentElement.remove()">×</button>
        `;
        suggestionEl.style.cssText = `
            position: fixed;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            background: #e3f2fd;
            color: #1976d2;
            padding: 10px 20px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 9999;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 14px;
        `;

        document.body.appendChild(suggestionEl);

        // 5秒后自动移除
        setTimeout(() => {
            if (suggestionEl.parentElement) {
                suggestionEl.remove();
            }
        }, 5000);
    }

    // 显示网络状态
    showNetworkStatus(message, type = 'info') {
        const statusEl = document.createElement('div');
        statusEl.className = `network-status ${type}`;
        statusEl.textContent = message;
        statusEl.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            padding: 10px 20px;
            border-radius: 5px;
            color: white;
            font-size: 14px;
            z-index: 9999;
            background: ${type === 'success' ? '#4caf50' : type === 'warning' ? '#ff9800' : '#2196f3'};
        `;

        document.body.appendChild(statusEl);

        setTimeout(() => {
            if (statusEl.parentElement) {
                statusEl.remove();
            }
        }, 3000);
    }

    // 显示错误信息
    showError(message) {
        const errorEl = document.createElement('div');
        errorEl.className = 'app-error';
        errorEl.innerHTML = `
            <h3>错误</h3>
            <p>${message}</p>
            <button onclick="location.reload()">刷新页面</button>
        `;
        errorEl.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            z-index: 10000;
            text-align: center;
            max-width: 400px;
        `;

        document.body.appendChild(errorEl);
    }

    // 获取应用统计信息
    async getAppStats() {
        try {
            const stats = await dbManager.getStats();
            const deviceInfo = {
                userAgent: navigator.userAgent,
                platform: navigator.platform,
                language: navigator.language,
                online: navigator.onLine,
                cookieEnabled: navigator.cookieEnabled,
                screenResolution: `${screen.width}x${screen.height}`,
                viewportSize: `${window.innerWidth}x${window.innerHeight}`
            };

            return {
                database: stats,
                device: deviceInfo,
                currentView: this.currentView,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('获取应用统计失败:', error);
            return null;
        }
    }

    // 导出应用数据
    async exportAppData() {
        try {
            const users = await dbManager.getAllUsers();
            const stats = await this.getAppStats();
            
            const exportData = {
                metadata: {
                    exportTime: new Date().toISOString(),
                    version: '1.0.0',
                    totalRecords: users.length
                },
                statistics: stats,
                users: users
            };

            const jsonStr = JSON.stringify(exportData, null, 2);
            const blob = new Blob([jsonStr], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const link = document.createElement('a');
            link.href = url;
            link.download = `数据采集系统_完整导出_${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            
            URL.revokeObjectURL(url);
            
            this.showNetworkStatus('应用数据导出成功', 'success');
        } catch (error) {
            console.error('导出应用数据失败:', error);
            this.showNetworkStatus('导出应用数据失败', 'error');
        }
    }

    // 清空所有数据
    async clearAllData() {
        if (!confirm('确定要清空所有数据吗？此操作不可恢复！')) {
            return;
        }

        if (!confirm('请再次确认：这将删除所有用户数据和照片，确定继续吗？')) {
            return;
        }

        try {
            await dbManager.clearAllData();
            this.showNetworkStatus('所有数据已清空', 'success');
            
            // 刷新当前视图
            if (this.currentView === 'tablet') {
                this.tabletController.loadData();
            } else {
                this.mobileController.clearForm();
            }
        } catch (error) {
            console.error('清空数据失败:', error);
            this.showNetworkStatus('清空数据失败', 'error');
        }
    }
}

// 全局变量
let appController;
let mobileController;
let tabletController;

// 应用启动
document.addEventListener('DOMContentLoaded', () => {
    appController = new AppController();
    
    // 将控制器实例设置为全局变量，供HTML中的事件处理器使用
    mobileController = appController.mobileController;
    tabletController = appController.tabletController;
    
    // 添加全局错误处理
    window.addEventListener('error', (e) => {
        console.error('全局错误:', e.error);
    });

    window.addEventListener('unhandledrejection', (e) => {
        console.error('未处理的Promise拒绝:', e.reason);
    });
});

// 导出全局函数供HTML使用
window.exportAppData = () => appController?.exportAppData();
window.clearAllData = () => appController?.clearAllData();
