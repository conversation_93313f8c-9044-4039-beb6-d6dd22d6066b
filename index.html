<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="format-detection" content="telephone=no">
    <title>数据采集系统</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>数据采集系统</h1>
            <nav>
                <button id="mobileBtn" class="nav-btn active">手机端录入</button>
                <button id="tabletBtn" class="nav-btn">平板端查看</button>
            </nav>
            <div class="help-links">
                <a href="mobile-guide.html" target="_blank" class="help-link">📱 手机端使用指南</a>
            </div>
        </header>

        <!-- 手机端数据录入界面 -->
        <div id="mobileView" class="view active">
            <div class="form-container">
                <h2>用户信息录入</h2>
                <form id="userForm">
                    <div class="form-group">
                        <label for="userName">姓名 *</label>
                        <input type="text" id="userName" name="userName" required>
                    </div>

                    <div class="form-group">
                        <label>性别 *</label>
                        <div class="radio-group">
                            <label class="radio-label">
                                <input type="radio" name="gender" value="男" required>
                                <span>男</span>
                            </label>
                            <label class="radio-label">
                                <input type="radio" name="gender" value="女" required>
                                <span>女</span>
                            </label>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>照片 *</label>
                        <div class="photo-section">
                            <input type="file" id="photoInput" accept="image/*" capture="camera" style="display: none;">
                            <button type="button" id="takePhotoBtn" class="photo-btn">
                                📷 拍照
                            </button>
                            <div id="photoPreview" class="photo-preview" style="display: none;">
                                <img id="previewImg" src="" alt="照片预览">
                                <button type="button" id="retakeBtn" class="retake-btn">重新拍照</button>
                            </div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" id="saveBtn" class="save-btn">保存数据</button>
                        <button type="button" id="clearBtn" class="clear-btn">清空表单</button>
                    </div>
                </form>

                <div id="statusMessage" class="status-message"></div>
            </div>
        </div>

        <!-- 平板端数据查看界面 -->
        <div id="tabletView" class="view">
            <div class="data-container">
                <h2>数据管理</h2>
                
                <div class="controls">
                    <div class="search-section">
                        <input type="text" id="searchInput" placeholder="搜索姓名...">
                        <button id="searchBtn">搜索</button>
                    </div>
                    
                    <div class="filter-section">
                        <select id="genderFilter">
                            <option value="">全部性别</option>
                            <option value="男">男</option>
                            <option value="女">女</option>
                        </select>
                        <button id="exportBtn">导出数据</button>
                    </div>
                </div>

                <div class="data-stats">
                    <span id="totalCount">总计: 0 条记录</span>
                </div>

                <div id="dataList" class="data-list">
                    <!-- 数据列表将在这里动态生成 -->
                </div>

                <div id="pagination" class="pagination">
                    <!-- 分页控件将在这里生成 -->
                </div>
            </div>
        </div>

        <!-- 详情查看模态框 -->
        <div id="detailModal" class="modal">
            <div class="modal-content">
                <span class="close">&times;</span>
                <h3>用户详情</h3>
                <div id="detailContent">
                    <!-- 详情内容将在这里显示 -->
                </div>
                <div class="modal-actions">
                    <button id="editBtn" class="edit-btn">编辑</button>
                    <button id="deleteBtn" class="delete-btn">删除</button>
                </div>
            </div>
        </div>
    </div>

    <script src="database.js"></script>
    <script src="mobile.js"></script>
    <script src="tablet.js"></script>
    <script src="app.js"></script>
</body>
</html>
