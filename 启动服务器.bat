@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    数据采集系统 - 服务器启动
echo ========================================
echo.

echo 正在获取本机IP地址...
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /c:"IPv4 地址"') do (
    for /f "tokens=1" %%b in ("%%a") do (
        set LOCAL_IP=%%b
        goto :found
    )
)
:found

echo.
echo ✅ 服务器启动成功！
echo.
echo 📱 手机访问地址：
echo    http://%LOCAL_IP%:8000
echo.
echo 💻 电脑访问地址：
echo    http://localhost:8000
echo    http://127.0.0.1:8000
echo.
echo 📋 使用说明：
echo    1. 确保电脑和手机连接同一WiFi
echo    2. 在手机浏览器输入上面的手机访问地址
echo    3. 如无法访问，请检查防火墙设置
echo.
echo 🔥 防火墙设置（如果需要）：
echo    1. Win+R 输入 wf.msc
echo    2. 入站规则 → 新建规则 → 端口 → TCP → 8000
echo    3. 允许连接
echo.
echo ⚠️  按 Ctrl+C 可停止服务器
echo ========================================
echo.

python -m http.server 8000 --bind 0.0.0.0
