<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>手机端使用指南</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 400px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }
        .header p {
            opacity: 0.9;
            font-size: 16px;
        }
        .guide-section {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }
        .guide-section h3 {
            font-size: 20px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .step {
            display: flex;
            align-items: flex-start;
            margin-bottom: 15px;
            padding: 15px;
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
        }
        .step-number {
            background: #4CAF50;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
            flex-shrink: 0;
        }
        .step-content {
            flex: 1;
        }
        .step-title {
            font-weight: 600;
            margin-bottom: 5px;
        }
        .step-desc {
            opacity: 0.9;
            font-size: 14px;
            line-height: 1.4;
        }
        .tips {
            background: rgba(255,193,7,0.2);
            border-left: 4px solid #FFC107;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .tips h4 {
            margin: 0 0 10px 0;
            color: #FFC107;
        }
        .tips ul {
            margin: 0;
            padding-left: 20px;
        }
        .tips li {
            margin-bottom: 5px;
            font-size: 14px;
        }
        .start-btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 18px;
            font-weight: 600;
            width: 100%;
            margin-top: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .start-btn:active {
            transform: scale(0.98);
        }
        .feature-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 20px;
        }
        .feature {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
        .feature-icon {
            font-size: 24px;
            margin-bottom: 8px;
        }
        .feature-text {
            font-size: 12px;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📱 手机端使用指南</h1>
            <p>快速上手数据采集系统</p>
        </div>

        <div class="guide-section">
            <h3>🚀 快速开始</h3>
            
            <div class="step">
                <div class="step-number">1</div>
                <div class="step-content">
                    <div class="step-title">填写姓名</div>
                    <div class="step-desc">在姓名输入框中输入用户姓名，支持语音输入</div>
                </div>
            </div>

            <div class="step">
                <div class="step-number">2</div>
                <div class="step-content">
                    <div class="step-title">选择性别</div>
                    <div class="step-desc">点击选择"男"或"女"，优化了触摸体验</div>
                </div>
            </div>

            <div class="step">
                <div class="step-number">3</div>
                <div class="step-content">
                    <div class="step-title">拍摄照片</div>
                    <div class="step-desc">点击"拍照"按钮直接调用相机，支持预览和重拍</div>
                </div>
            </div>

            <div class="step">
                <div class="step-number">4</div>
                <div class="step-content">
                    <div class="step-title">保存数据</div>
                    <div class="step-desc">确认信息无误后点击"保存数据"，数据会存储在本地</div>
                </div>
            </div>
        </div>

        <div class="guide-section">
            <h3>💡 实用功能</h3>
            
            <div class="feature-grid">
                <div class="feature">
                    <div class="feature-icon">📸</div>
                    <div class="feature-text">一键拍照<br>直接调用相机</div>
                </div>
                <div class="feature">
                    <div class="feature-icon">🎤</div>
                    <div class="feature-text">语音输入<br>说出姓名即可</div>
                </div>
                <div class="feature">
                    <div class="feature-icon">💾</div>
                    <div class="feature-text">本地存储<br>无需网络连接</div>
                </div>
                <div class="feature">
                    <div class="feature-icon">🔄</div>
                    <div class="feature-text">重拍功能<br>不满意可重拍</div>
                </div>
            </div>
        </div>

        <div class="tips">
            <h4>🔥 使用技巧</h4>
            <ul>
                <li>双击姓名输入框可快速清空</li>
                <li>拍照后可以预览，不满意点击"重新拍照"</li>
                <li>数据保存后会有震动反馈（支持的设备）</li>
                <li>横屏时界面会自动调整布局</li>
                <li>支持离线使用，数据存储在手机本地</li>
                <li>可以随时切换到"平板端查看"管理数据</li>
            </ul>
        </div>

        <div class="guide-section">
            <h3>⚠️ 注意事项</h3>
            <ul style="margin: 0; padding-left: 20px;">
                <li>首次使用需要允许相机权限</li>
                <li>照片会自动压缩以节省存储空间</li>
                <li>清除浏览器数据会删除已保存的信息</li>
                <li>建议定期导出数据进行备份</li>
            </ul>
        </div>

        <button class="start-btn" onclick="window.location.href='index.html'">
            开始使用 →
        </button>
    </div>

    <script>
        // 添加触觉反馈
        if ('vibrate' in navigator) {
            document.querySelector('.start-btn').addEventListener('click', () => {
                navigator.vibrate(50);
            });
        }

        // 检测设备方向变化
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                window.scrollTo(0, 0);
            }, 100);
        });
    </script>
</body>
</html>
