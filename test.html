<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            padding: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            margin: 0;
        }
        .container {
            background: rgba(255,255,255,0.1);
            padding: 40px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            max-width: 400px;
            margin: 0 auto;
        }
        h1 {
            font-size: 2.5em;
            margin-bottom: 20px;
        }
        .success {
            font-size: 1.2em;
            margin: 20px 0;
        }
        .btn {
            background: #4CAF50;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 18px;
            cursor: pointer;
            margin: 10px;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #45a049;
            transform: translateY(-2px);
        }
        .info {
            background: rgba(255,255,255,0.2);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉</h1>
        <h2>连接成功！</h2>
        <div class="success">
            恭喜！你的手机已经成功连接到数据采集系统。
        </div>
        
        <div class="info">
            <strong>当前设备信息：</strong><br>
            <span id="deviceInfo"></span>
        </div>
        
        <a href="index.html" class="btn">进入数据采集系统</a>
        <a href="mobile-guide.html" class="btn">查看使用指南</a>
        
        <div style="margin-top: 30px; font-size: 14px; opacity: 0.8;">
            如果你看到这个页面，说明网络连接正常！
        </div>
    </div>

    <script>
        // 显示设备信息
        const deviceInfo = document.getElementById('deviceInfo');
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        const platform = navigator.platform || 'Unknown';
        const userAgent = navigator.userAgent;
        
        let deviceType = '未知设备';
        if (/iPhone/i.test(userAgent)) deviceType = 'iPhone';
        else if (/iPad/i.test(userAgent)) deviceType = 'iPad';
        else if (/Android/i.test(userAgent)) deviceType = 'Android设备';
        else if (!isMobile) deviceType = '桌面设备';
        
        deviceInfo.innerHTML = `
            设备类型: ${deviceType}<br>
            屏幕尺寸: ${screen.width}×${screen.height}<br>
            浏览器: ${navigator.userAgent.split(' ')[0]}
        `;

        // 添加触觉反馈
        if ('vibrate' in navigator) {
            document.querySelectorAll('.btn').forEach(btn => {
                btn.addEventListener('click', () => {
                    navigator.vibrate(50);
                });
            });
        }
    </script>
</body>
</html>
