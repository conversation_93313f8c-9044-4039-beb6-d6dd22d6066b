# 数据采集系统

一个基于Web技术的双端数据采集和展示系统，支持移动端数据录入和平板端数据查看管理。

## 功能特性

### 手机端（数据录入）
- ✅ 用户信息录入（姓名、性别）
- ✅ 一键相机拍照功能
- ✅ 照片预览和重拍
- ✅ 图片自动压缩优化
- ✅ 数据本地存储
- ✅ 实时表单验证
- ✅ 完全离线支持
- ✅ 语音输入姓名（支持的设备）
- ✅ 触觉反馈体验
- ✅ 防双击缩放优化
- ✅ 横竖屏自适应
- ✅ 专用使用指南

### 平板端（数据查看）
- ✅ 数据列表展示
- ✅ 搜索和筛选
- ✅ 分页显示
- ✅ 详情查看
- ✅ 数据编辑和删除
- ✅ 数据导出（CSV格式）
- ✅ 统计信息显示

## 技术架构

### 前端技术栈
- **HTML5**: 页面结构和语义化标记
- **CSS3**: 响应式设计和现代UI样式
- **JavaScript (ES6+)**: 应用逻辑和交互功能
- **IndexedDB**: 本地数据存储
- **File API**: 文件处理和图片操作
- **Media Devices API**: 相机调用

### 数据存储
- **IndexedDB**: 浏览器本地数据库
- **Base64编码**: 图片存储格式
- **自动压缩**: 图片大小优化

## 文件结构

```
├── index.html          # 主页面文件
├── styles.css          # 样式文件
├── app.js             # 主应用控制器
├── database.js        # 数据库管理模块
├── mobile.js          # 手机端功能模块
├── tablet.js          # 平板端功能模块
├── mobile-guide.html  # 手机端使用指南
└── README.md          # 项目说明文档
```

## 快速开始

1. **部署应用**
   ```bash
   # 将所有文件放在Web服务器目录下
   # 或者使用本地服务器
   python -m http.server 8000
   # 或
   npx serve .
   ```

2. **访问应用**
   - 在浏览器中打开 `http://localhost:8000`
   - 手机端建议使用移动设备访问
   - 平板端建议使用平板或大屏设备访问

3. **开始使用**
   - 点击"手机端录入"进行数据录入
   - 点击"平板端查看"查看和管理数据

## 使用说明

### 📱 手机端数据录入流程
1. **输入姓名**: 在输入框中输入用户姓名
   - 支持语音输入（点击🎤按钮）
   - 双击输入框可快速清空
2. **选择性别**: 点击选择"男"或"女"
   - 优化了触摸体验，按钮更大更易点击
3. **拍摄照片**: 点击"拍照"按钮
   - 自动调用设备相机
   - 支持前后摄像头切换
4. **预览确认**: 查看拍摄的照片
   - 不满意可点击"重新拍照"
   - 照片会自动压缩优化
5. **保存数据**: 点击"保存数据"
   - 有触觉反馈（支持的设备）
   - 数据保存到本地数据库

### 📱 手机端特色功能
- **一键拍照**: 直接调用相机，无需文件选择
- **语音输入**: 支持语音识别输入姓名
- **触觉反馈**: 操作时有轻微震动反馈
- **防误触**: 防止双击缩放，优化触摸体验
- **自适应**: 横竖屏自动调整布局
- **离线使用**: 完全本地存储，无需网络

### 数据管理功能
1. **查看数据**: 在平板端可以看到所有录入的数据
2. **搜索**: 在搜索框中输入姓名进行搜索
3. **筛选**: 使用性别筛选器过滤数据
4. **详情**: 点击数据卡片查看详细信息
5. **编辑**: 在详情页面可以编辑用户信息
6. **删除**: 在详情页面可以删除用户数据
7. **导出**: 点击"导出数据"下载CSV文件

## 数据库设计

### 用户信息表 (userInfo)
```javascript
{
  id: Number,           // 自增主键
  name: String,         // 用户姓名
  gender: String,       // 性别 ('男' | '女')
  photo: {              // 照片信息
    data: String,       // Base64编码的图片数据
    name: String,       // 文件名
    size: Number,       // 文件大小
    type: String        // MIME类型
  },
  createTime: String,   // 创建时间 (ISO格式)
  updateTime: String    // 更新时间 (ISO格式)
}
```

## 浏览器兼容性

### 支持的浏览器
- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 11+
- ✅ Edge 79+
- ✅ 移动端浏览器 (iOS Safari, Chrome Mobile)

### 必需的API支持
- IndexedDB
- File API
- Canvas API
- Media Devices API (相机功能)

## 功能特点

### 响应式设计
- 自适应不同屏幕尺寸
- 移动端优化的触摸交互
- 平板端优化的数据展示

### 离线支持
- 数据完全存储在本地
- 无需网络连接即可使用
- 自动检测网络状态

### 数据安全
- 数据仅存储在用户设备上
- 不会上传到任何服务器
- 支持数据导出和备份

## 开发说明

### 扩展功能
如需添加新功能，可以：
1. 在相应的控制器类中添加方法
2. 在HTML中添加对应的UI元素
3. 在CSS中添加样式
4. 在数据库模块中添加新的数据操作方法

### 自定义样式
修改 `styles.css` 文件可以自定义界面样式：
- 颜色主题
- 字体样式
- 布局调整
- 动画效果

## 故障排除

### 常见问题
1. **相机无法调用**: 检查浏览器权限设置
2. **数据丢失**: 检查浏览器存储设置，避免清除浏览器数据
3. **图片显示异常**: 检查图片格式是否支持
4. **性能问题**: 清理旧数据或减少图片大小

### 调试模式
在浏览器开发者工具中可以：
- 查看控制台日志
- 检查IndexedDB数据
- 监控网络状态
- 调试JavaScript代码

## 许可证

本项目采用 MIT 许可证。

## 更新日志

### v1.0.0 (2024-08-25)
- ✅ 初始版本发布
- ✅ 基础数据录入功能
- ✅ 数据查看和管理功能
- ✅ 响应式设计
- ✅ 离线支持
